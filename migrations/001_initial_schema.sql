-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (managed by <PERSON><PERSON><PERSON> auth)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    address JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create accounts table
CREATE TABLE accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    balance DECIMAL(15,2) NOT NULL DEFAULT 0,
    account_number TEXT NOT NULL,
    routing_number TEXT,
    credit_limit DECIMAL(15,2),
    available_credit DECIMAL(15,2),
    due_date DATE,
    minimum_payment DECIMAL(15,2),
    apr DECIMAL(5,2),
    ytd_contributions DECIMAL(15,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active'
);

-- Create transactions table
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    amount DECIMAL(15,2) NOT NULL,
    type TEXT NOT NULL,
    description TEXT NOT NULL,
    date DATE NOT NULL,
    category TEXT NOT NULL,
    merchant TEXT,
    location TEXT,
    reference TEXT,
    pending BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create transfers table
CREATE TABLE transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    to_account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    amount DECIMAL(15,2) NOT NULL,
    date DATE NOT NULL,
    status TEXT NOT NULL,
    description TEXT NOT NULL,
    confirmation_code TEXT NOT NULL,
    type TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create account balance history table
CREATE TABLE account_balance_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    balance DECIMAL(15,2) NOT NULL,
    change_amount DECIMAL(15,2) NOT NULL,
    change_type TEXT NOT NULL,
    reference_id UUID NOT NULL,
    reference_type TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_accounts_user_id ON accounts(user_id);
CREATE INDEX idx_transactions_account_id ON transactions(account_id);
CREATE INDEX idx_transfers_from_account_id ON transfers(from_account_id);
CREATE INDEX idx_transfers_to_account_id ON transfers(to_account_id);
CREATE INDEX idx_balance_history_account_id ON account_balance_history(account_id);

-- Function to generate account number
CREATE OR REPLACE FUNCTION generate_account_number()
RETURNS TEXT AS $$
DECLARE
    new_account_number TEXT;
BEGIN
    -- Generate a random 10-digit number
    new_account_number := LPAD(FLOOR(RANDOM() * **********0)::TEXT, 10, '0');
    
    -- Check if it exists
    WHILE EXISTS (SELECT 1 FROM accounts WHERE account_number = new_account_number) LOOP
        new_account_number := LPAD(FLOOR(RANDOM() * **********0)::TEXT, 10, '0');
    END LOOP;
    
    RETURN new_account_number;
END;
$$ LANGUAGE plpgsql;

-- Function to generate routing number
CREATE OR REPLACE FUNCTION generate_routing_number()
RETURNS TEXT AS $$
DECLARE
    new_routing_number TEXT;
BEGIN
    -- Generate a random 9-digit number
    new_routing_number := LPAD(FLOOR(RANDOM() * **********)::TEXT, 9, '0');
    
    -- Check if it exists
    WHILE EXISTS (SELECT 1 FROM accounts WHERE routing_number = new_routing_number) LOOP
        new_routing_number := LPAD(FLOOR(RANDOM() * **********)::TEXT, 9, '0');
    END LOOP;
    
    RETURN new_routing_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update account balance
CREATE OR REPLACE FUNCTION update_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- Update account balance
    UPDATE accounts
    SET balance = balance + NEW.amount
    WHERE id = NEW.account_id;

    -- Record balance change
    INSERT INTO account_balance_history (
        account_id,
        balance,
        change_amount,
        change_type,
        reference_id,
        reference_type
    )
    VALUES (
        NEW.account_id,
        (SELECT balance FROM accounts WHERE id = NEW.account_id),
        NEW.amount,
        'transaction',
        NEW.id,
        'transaction'
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for transactions
CREATE TRIGGER transaction_balance_update
AFTER INSERT ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_account_balance();

-- Function to handle transfers
CREATE OR REPLACE FUNCTION handle_transfer()
RETURNS TRIGGER AS $$
BEGIN
    -- Deduct from source account
    UPDATE accounts
    SET balance = balance - NEW.amount
    WHERE id = NEW.from_account_id;

    -- Add to destination account
    UPDATE accounts
    SET balance = balance + NEW.amount
    WHERE id = NEW.to_account_id;

    -- Record balance changes
    INSERT INTO account_balance_history (
        account_id,
        balance,
        change_amount,
        change_type,
        reference_id,
        reference_type
    )
    VALUES 
    (
        NEW.from_account_id,
        (SELECT balance FROM accounts WHERE id = NEW.from_account_id),
        -NEW.amount,
        'transfer',
        NEW.id,
        'transfer'
    ),
    (
        NEW.to_account_id,
        (SELECT balance FROM accounts WHERE id = NEW.to_account_id),
        NEW.amount,
        'transfer',
        NEW.id,
        'transfer'
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for transfers
CREATE TRIGGER transfer_balance_update
AFTER INSERT ON transfers
FOR EACH ROW
EXECUTE FUNCTION handle_transfer();

-- Disable RLS for all tables
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE accounts DISABLE ROW LEVEL SECURITY;
ALTER TABLE transactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE transfers DISABLE ROW LEVEL SECURITY;
ALTER TABLE account_balance_history DISABLE ROW LEVEL SECURITY; 