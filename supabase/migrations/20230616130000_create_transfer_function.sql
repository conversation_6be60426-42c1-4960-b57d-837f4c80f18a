-- Create a function to handle transfers atomically
create or replace function execute_transfer(
  transfer_data jsonb,
  from_tx jsonb,
  to_tx jsonb
) returns jsonb
language plpgsql
security definer
as $$
declare
  transfer_id uuid;
  from_tx_id uuid;
  to_tx_id uuid;
  result jsonb;
begin
  -- Start a transaction
  begin
    -- Insert the transfer record
    insert into transfers (
      id, from_account, to_account, amount, date, status, description, "confirmationCode", type
    ) values (
      (transfer_data->>'id')::uuid,
      transfer_data->>'fromAccount',
      transfer_data->>'toAccount',
      (transfer_data->>'amount')::numeric,
      (transfer_data->>'date')::date,
      transfer_data->>'status',
      transfer_data->>'description',
      transfer_data->>'confirmationCode',
      transfer_data->>'type'
    )
    returning id into transfer_id;
    
    -- Insert the 'from' transaction
    insert into transactions (
      account_id, amount, type, description, date, category, reference
    ) values (
      from_tx->>'accountId',
      (from_tx->>'amount')::numeric,
      from_tx->>'type',
      from_tx->>'description',
      (from_tx->>'date')::timestamptz,
      from_tx->>'category',
      from_tx->>'reference'
    )
    returning id into from_tx_id;
    
    -- Insert the 'to' transaction
    insert into transactions (
      account_id, amount, type, description, date, category, reference
    ) values (
      to_tx->>'accountId',
      (to_tx->>'amount')::numeric,
      to_tx->>'type',
      to_tx->>'description',
      (to_tx->>'date')::timestamptz,
      to_tx->>'category',
      to_tx->>'reference'
    )
    returning id into to_tx_id;
    
    -- Update account balances
    update accounts 
    set balance = balance - (transfer_data->>'amount')::numeric
    where id = transfer_data->>'fromAccount';
    
    update accounts 
    set balance = balance + (transfer_data->>'amount')::numeric
    where id = transfer_data->>'toAccount';
    
    -- Return success with IDs
    result := jsonb_build_object(
      'success', true,
      'transferId', transfer_id,
      'fromTransactionId', from_tx_id,
      'toTransactionId', to_tx_id
    );
    
    return result;
    
  exception when others then
    -- Rollback on error
    raise exception 'Transfer failed: %', sqlerrm;
  end;
end;
$$;
