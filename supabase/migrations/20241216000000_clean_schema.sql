-- Drop existing tables and functions to start clean
DROP TABLE IF EXISTS account_balance_history CASCADE;
DROP TABLE IF EXIS<PERSON> transfers CASCADE;
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS accounts CASCADE;
DROP TABLE IF EXISTS sessions CASCADE;
DROP TABLE IF EXISTS users CASCADE;

DROP FUNCTION IF EXISTS execute_transfer CASCADE;
DROP FUNCTION IF EXISTS generate_reference_code CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column CASCADE;
DROP FUNCTION IF EXISTS handle_transfer CASCADE;
DROP FUNCTION IF EXISTS update_account_balance CASCADE;
DROP FUNCTION IF EXISTS generate_account_number CASCADE;
DROP FUNCTION IF EXISTS generate_routing_number CASCADE;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for plain text authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    name TEXT,
    email TEXT,
    phone TEXT,
    address JSONB,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sessions table for user authentication
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Accounts table
CREATE TABLE accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('checking', 'savings', 'credit', 'investment', 'loan')),
    balance DECIMAL(15, 2) NOT NULL DEFAULT 0,
    account_number TEXT NOT NULL UNIQUE,
    routing_number TEXT,
    credit_limit DECIMAL(15, 2),
    available_credit DECIMAL(15, 2),
    due_date DATE,
    minimum_payment DECIMAL(15, 2),
    apr DECIMAL(5, 2),
    ytd_contributions DECIMAL(15, 2),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'closed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    amount DECIMAL(15, 2) NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('credit', 'debit')),
    description TEXT NOT NULL,
    date DATE NOT NULL,
    category TEXT,
    merchant TEXT,
    location TEXT,
    reference TEXT,
    pending BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transfers table
CREATE TABLE transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_account_id UUID REFERENCES accounts(id) ON DELETE SET NULL,
    to_account_id UUID REFERENCES accounts(id) ON DELETE SET NULL,
    amount DECIMAL(15, 2) NOT NULL,
    date DATE NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    description TEXT,
    reference_code TEXT,
    type TEXT CHECK (type IN ('internal', 'external')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Account balance history
CREATE TABLE account_balance_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    balance DECIMAL(15, 2) NOT NULL,
    change_amount DECIMAL(15, 2) NOT NULL,
    change_type TEXT NOT NULL,
    reference_id UUID,
    reference_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_sessions_token ON sessions(token);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_accounts_user_id ON accounts(user_id);
CREATE INDEX idx_accounts_account_number ON accounts(account_number);
CREATE INDEX idx_transactions_account_id ON transactions(account_id);
CREATE INDEX idx_transactions_date ON transactions(date);
CREATE INDEX idx_transfers_from_account_id ON transfers(from_account_id);
CREATE INDEX idx_transfers_to_account_id ON transfers(to_account_id);
CREATE INDEX idx_balance_history_account_id ON account_balance_history(account_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_accounts_updated_at
BEFORE UPDATE ON accounts
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sessions_updated_at
BEFORE UPDATE ON sessions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate account number
CREATE OR REPLACE FUNCTION generate_account_number()
RETURNS TEXT AS $$
DECLARE
    new_account_number TEXT;
BEGIN
    LOOP
        new_account_number := LPAD(FLOOR(RANDOM() * ***********)::TEXT, 10, '0');
        EXIT WHEN NOT EXISTS (SELECT 1 FROM accounts WHERE account_number = new_account_number);
    END LOOP;
    RETURN new_account_number;
END;
$$ LANGUAGE plpgsql;

-- Function to generate reference code
CREATE OR REPLACE FUNCTION generate_reference_code()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER := 0;
BEGIN
    FOR i IN 1..12 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
        IF i % 4 = 0 AND i < 12 THEN
            result := result || '-';
        END IF;
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to handle balance updates and history
CREATE OR REPLACE FUNCTION update_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- Update account balance
    UPDATE accounts
    SET balance = balance + NEW.amount
    WHERE id = NEW.account_id;

    -- Record balance change in history
    INSERT INTO account_balance_history (
        account_id,
        balance,
        change_amount,
        change_type,
        reference_id,
        reference_type
    )
    VALUES (
        NEW.account_id,
        (SELECT balance FROM accounts WHERE id = NEW.account_id),
        NEW.amount,
        'transaction',
        NEW.id,
        'transaction'
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic balance updates
CREATE TRIGGER transaction_balance_update
AFTER INSERT ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_account_balance();

-- Function to execute transfers atomically
CREATE OR REPLACE FUNCTION execute_transfer(
    from_account UUID,
    to_account UUID,
    transfer_amount DECIMAL,
    transfer_description TEXT,
    transfer_type TEXT DEFAULT 'internal'
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    transfer_id UUID;
    from_tx_id UUID;
    to_tx_id UUID;
    reference_code TEXT;
    transfer_date DATE;
    result JSONB;
BEGIN
    -- Generate reference code and date
    reference_code := generate_reference_code();
    transfer_date := CURRENT_DATE;
    
    -- Start transaction
    BEGIN
        -- Insert transfer record
        INSERT INTO transfers (
            from_account_id, to_account_id, amount, date, status, 
            description, reference_code, type
        ) VALUES (
            from_account, to_account, transfer_amount, transfer_date, 'completed',
            transfer_description, reference_code, transfer_type
        )
        RETURNING id INTO transfer_id;
        
        -- Insert debit transaction (from account)
        INSERT INTO transactions (
            account_id, amount, type, description, date, category, reference
        ) VALUES (
            from_account, -transfer_amount, 'debit', 
            'Transfer to ' || (SELECT account_number FROM accounts WHERE id = to_account),
            transfer_date, 'Transfer', reference_code
        )
        RETURNING id INTO from_tx_id;
        
        -- Insert credit transaction (to account)
        INSERT INTO transactions (
            account_id, amount, type, description, date, category, reference
        ) VALUES (
            to_account, transfer_amount, 'credit',
            'Transfer from ' || (SELECT account_number FROM accounts WHERE id = from_account),
            transfer_date, 'Transfer', reference_code
        )
        RETURNING id INTO to_tx_id;
        
        -- Return success
        result := jsonb_build_object(
            'success', true,
            'transfer_id', transfer_id,
            'from_transaction_id', from_tx_id,
            'to_transaction_id', to_tx_id,
            'reference_code', reference_code
        );
        
        RETURN result;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Transfer failed: %', SQLERRM;
    END;
END;
$$;

-- Insert default admin user
INSERT INTO users (username, password, name, email, is_admin)
VALUES ('admin', 'admin123', 'System Administrator', '<EMAIL>', true)
ON CONFLICT (username) DO NOTHING;

-- Disable RLS for all tables (as requested)
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE accounts DISABLE ROW LEVEL SECURITY;
ALTER TABLE transactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE transfers DISABLE ROW LEVEL SECURITY;
ALTER TABLE account_balance_history DISABLE ROW LEVEL SECURITY;
