import { useState, useEffect, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, Outlet, useLocation } from 'react-router-dom';
import { Header } from './components/Header';
import { Navigation } from './components/Navigation';
import { AdminLayout } from './components/AdminLayout';
import { Dashboard } from './pages/Dashboard';
import { Transfers } from './pages/Transfers';
import { Transactions } from './pages/Transactions';
import { Settings } from './pages/Settings';
import { Login } from './pages/Login';
import { AdminLogin } from './pages/AdminLogin';
import { AdminDashboard } from './pages/admin/AdminDashboard';
import { UserManagement } from './pages/admin/UserManagement';
import { AccountManagement } from './pages/admin/AccountManagement';
import { getSession, logout, getAdminSession, adminLogout, AdminSession } from './lib/auth';
import { User, Account, Transaction, Transfer, TransferResult } from './types';
import { supabase } from './lib/supabase';

// Layout component for authenticated routes
function AppLayout({ user, onLogout, onTabChange }: { user: User; onLogout: () => void; onTabChange: (tab: string) => void }) {
  const location = useLocation();
  const activeTab = location.pathname.substring(1) || 'dashboard';

  return (
    <div className="min-h-screen bg-gray-100">
      <Header user={user} onLogout={onLogout} />
      <div className="flex">
        <Navigation
          activeTab={activeTab}
          onTabChange={onTabChange}
          onLogout={onLogout}
          isAdmin={user?.is_admin}
        />
        <main className="flex-1 p-6 md:ml-80 pt-20 md:pt-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

function AppContent() {
  const navigate = useNavigate();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transfers, setTransfers] = useState<Transfer[]>([]);

  const handleLogout = useCallback(async () => {
    await logout();
    setUser(null);
    setIsLoggedIn(false);
    navigate('/login');
  }, [navigate]);

  const loadDataForUser = useCallback(async (userId: string) => {
    try {
      const { data: accountsData, error: accountsError } = await supabase
        .from('accounts')
        .select('*')
        .eq('user_id', userId);

      if (accountsError) throw accountsError;
      setAccounts(accountsData || []);
      const accountIds = (accountsData || []).map(a => a.id);

      if (accountIds.length > 0) {
        const [transactionsRes, transfersRes] = await Promise.all([
          supabase.from('transactions').select('*').in('account_id', accountIds).order('date', { ascending: false }),
          supabase.from('transfers').select('*').or(`from_account_id.in.(${accountIds.join(',')}),to_account_id.in.(${accountIds.join(',')})`).order('created_at', { ascending: false })
        ]);

        if (transactionsRes.error) throw transactionsRes.error;
        setTransactions(transactionsRes.data || []);

        if (transfersRes.error) throw transfersRes.error;
        setTransfers(transfersRes.data || []);
      } else {
        setTransactions([]);
        setTransfers([]);
      }
    } catch (error) {
      console.error("Error loading user data:", error);
      await handleLogout();
    }
  }, [handleLogout]);

  const checkUserSession = useCallback(async (isInitialLoad = false) => {
    if (isInitialLoad) setInitialLoading(true);
    const token = localStorage.getItem('token');
    if (token) {
      const session = await getSession(token);
      if (session && session.user_id) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user_id)
          .single();

        if (userData && !userError) {
          setUser(userData);
          setIsLoggedIn(true);
          await loadDataForUser(userData.id);
        } else {
          await handleLogout();
        }
      } else {
        await handleLogout();
      }
    } else {
      setIsLoggedIn(false);
      setUser(null);
    }
    if (isInitialLoad) setInitialLoading(false);
  }, [loadDataForUser, handleLogout]);

  useEffect(() => {
    checkUserSession(true); // Only show loading on initial load
  }, [checkUserSession]);

  const handleTabChange = useCallback((tab: string) => {
    navigate(tab === 'dashboard' ? '/' : `/${tab}`);
  }, [navigate]);

  const handleTransfer = async (transferData: { fromAccount: string; toAccount: string; amount: number; description: string; type: 'internal' | 'external'; }) => {
    if (!user) return { success: false, error: 'User not authenticated' };
    try {
      const { data, error } = await supabase.rpc('execute_transfer', {
        from_account: transferData.fromAccount,
        to_account: transferData.toAccount,
        transfer_amount: transferData.amount,
        transfer_description: transferData.description,
        transfer_type: transferData.type
      });

      if (error) throw error;

      console.log('Transfer completed:', data);
      await loadDataForUser(user.id);
      return { success: true };
    } catch (error) {
      console.error('Transfer error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Transfer failed' };
    }
  };

  const handleUserUpdate = async (updatedUser: Partial<User>) => {
    if (!user) return { error: 'No user logged in' };
    try {
      const { data, error } = await supabase.from('users').update(updatedUser).eq('id', user.id).select().single();
      if (error) throw error;
      if (data) setUser(data);
      return { success: true, user: data };
    } catch (error) {
      console.error('Error updating user:', error);
      return { success: false, error };
    }
  };

  const handleAccountClick = useCallback((accountId: string) => {
    navigate('/transactions', { state: { accountId } });
  }, [navigate]);


  return (
    <Routes>
      <Route path="/login" element={isLoggedIn ? <Navigate to="/" /> : <Login onLoginSuccess={checkUserSession} />} />
      {isLoggedIn && user ? (
        <Route path="/*" element={<AppLayout user={user} onLogout={handleLogout} onTabChange={handleTabChange} />}>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard accounts={accounts} transactions={transactions} user={user} onAccountClick={handleAccountClick} onTabChange={handleTabChange} />} />
          <Route path="transfers" element={<Transfers transfers={transfers} accounts={accounts} onTransfer={handleTransfer} />} />
          <Route path="transactions" element={<Transactions transactions={transactions} accounts={accounts} />} />
          <Route path="settings" element={<Settings user={user} onUserUpdate={handleUserUpdate} />} />
        </Route>
      ) : (
        <Route path="*" element={<Navigate to="/login" replace />} />
      )}
    </Routes>
  );
}

// Admin Content Component
function AdminContent() {
  const navigate = useNavigate();
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(false);
  const [adminSession, setAdminSession] = useState<AdminSession | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  const handleAdminLogout = useCallback(async () => {
    await adminLogout();
    setAdminSession(null);
    setIsAdminLoggedIn(false);
    navigate('/admin/login');
  }, [navigate]);

  const checkAdminSession = useCallback(async (isInitialLoad = false) => {
    if (isInitialLoad) setInitialLoading(true);
    const session = await getAdminSession();
    if (session) {
      setAdminSession(session);
      setIsAdminLoggedIn(true);
    } else {
      setIsAdminLoggedIn(false);
      setAdminSession(null);
    }
    if (isInitialLoad) setInitialLoading(false);
  }, []);

  useEffect(() => {
    checkAdminSession(true); // Only show loading on initial load
  }, [checkAdminSession]);

  const handleAdminNavigate = useCallback((page: string) => {
    navigate(`/admin/${page}`);
  }, [navigate]);

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <Routes>
      <Route path="/admin/login" element={isAdminLoggedIn ? <Navigate to="/admin/dashboard" /> : <AdminLogin onLoginSuccess={checkAdminSession} />} />
      {isAdminLoggedIn && adminSession ? (
        <Route path="/admin/*" element={<AdminLayout onLogout={handleAdminLogout} />}>
          <Route index element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="dashboard" element={<AdminDashboard onNavigate={handleAdminNavigate} />} />
          <Route path="users" element={<UserManagement onNavigate={handleAdminNavigate} />} />
          <Route path="accounts" element={<AccountManagement onNavigate={handleAdminNavigate} />} />
          <Route path="transactions" element={<div className="text-center py-12"><h2 className="text-2xl font-bold text-gray-900">Transaction Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>} />
        </Route>
      ) : (
        <Route path="/admin/*" element={<Navigate to="/admin/login" replace />} />
      )}
    </Routes>
  );
}

function App() {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div>
      {isAdminRoute ? <AdminContent /> : <AppContent />}
    </div>
  );
}

function AppWrapper() {
  return (
    <Router>
      <App />
    </Router>
  );
}

export default AppWrapper;