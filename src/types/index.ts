// ==============================================
// TYPE DEFINITIONS
// ==============================================
// Central location for all TypeScript interfaces and types
// Modify these types to match your financial product requirements

export interface Account {
  id: string;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'investment';
  balance: number;
  accountNumber: string;
  routingNumber?: string;
  availableCredit?: number;
  creditLimit?: number;
  dueDate?: string;
  minimumPayment?: number;
  apr?: number;
  ytdContributions?: number;
}

export interface Transaction {
  id: string;
  accountId: string;
  amount: number;
  type: 'debit' | 'credit';
  description: string;
  date: string;
  category: string;
  pending?: boolean;
  merchant?: string;
  location?: string;
  reference?: string;
}

export interface Transfer {
  id: string;
  fromAccount: string;
  toAccount: string;
  amount: number;
  date: string;
  status: 'completed' | 'pending' | 'scheduled';
  description: string;
  confirmationCode: string;
  type: 'internal' | 'external';
}

export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
  };
  is_admin: boolean;
  created_at: string;
  updated_at: string;
}

export interface TransferResult {
  success: boolean;
  transferId: string;
  fromTransactionId: string;
  toTransactionId: string;
}