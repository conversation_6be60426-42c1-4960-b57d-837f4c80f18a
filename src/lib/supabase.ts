import { createClient } from '@supabase/supabase-js'

// Supabase client configuration
export const supabaseUrl = 'https://umwijruhlqflklikyena.supabase.co'
export const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rB_G_tagKgiQgZ8eS-RZKCQibCKs_FJRvJlcTwyEe1Y'

export const supabase = createClient(supabaseUrl, supabaseKey)

// Type definitions for Supabase tables
export interface User {
  id: string
  name: string
  email: string
  phone: string
  address: any
  created_at: string
}

export interface Account {
  id: string
  user_id: string
  name: string
  type: string
  balance: number
  account_number: string
  routing_number: string | null
  credit_limit: number | null
  available_credit: number | null
  due_date: string | null
  minimum_payment: number | null
  apr: number | null
  ytd_contributions: number | null
  created_at: string
  status: string
}

export interface Transaction {
  id: string
  account_id: string
  amount: number
  type: string
  description: string
  date: string
  category: string
  merchant: string | null
  location: string | null
  reference: string
  receipt_number: string
  pending: boolean
  created_at: string
  batch_id: string | null
}
