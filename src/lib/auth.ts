import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

export interface UserSession {
  id: string;
  user_id: string;
  token: string;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

export interface AdminSession {
  user: {
    id: string;
    email: string;
    user_metadata?: any;
  };
  access_token: string;
  refresh_token: string;
}

export async function login(username: string, password: string): Promise<{ session: UserSession | null; error: Error | null }> {
  try {
    // Get user by username and password (plain text for now)
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .eq('password', password)
      .single();

    if (userError || !userData) {
      return { session: null, error: new Error('Invalid username or password') };
    }

    // Create a new session
    const token = uuidv4();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiration

    const { data: sessionData, error: sessionError } = await supabase
      .from('sessions')
      .insert({
        user_id: userData.id,
        token,
        expires_at: expiresAt.toISOString(),
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Session creation error:', sessionError);
      return { session: null, error: new Error('Failed to create session') };
    }

    return { session: sessionData, error: null };
  } catch (error) {
    console.error('Login error:', error);
    return { session: null, error: error as Error };
  }
}

export async function getSession(token: string): Promise<UserSession | null> {
  try {
    const { data, error } = await supabase
      .from('sessions')
      .select('*')
      .eq('token', token)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data) {
      return null;
    }

    return data;
  } catch (error) {
    console.error('Get session error:', error);
    return null;
  }
}

export async function logout(): Promise<{ error: Error | null }> {
  const token = localStorage.getItem('token');
  if (!token) return { error: null }; // Not logged in
  try {
    const { error } = await supabase
      .from('sessions')
      .delete()
      .eq('token', token);

    localStorage.removeItem('token');
    if (error) {
      console.error('Logout error:', error);
      return { error };
    }
    return { error: null };
  } catch (error) {
    console.error('Logout error:', error);
    localStorage.removeItem('token'); // Ensure token is removed on error
    return { error: error as Error };
  }
}

// Admin authentication functions using Supabase Auth
export async function adminLogin(email: string, password: string): Promise<{ session: AdminSession | null; error: Error | null }> {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return { session: null, error: new Error(error.message) };
    }

    if (!data.session || !data.user) {
      return { session: null, error: new Error('No session created') };
    }

    const adminSession: AdminSession = {
      user: {
        id: data.user.id,
        email: data.user.email || '',
        user_metadata: data.user.user_metadata,
      },
      access_token: data.session.access_token,
      refresh_token: data.session.refresh_token,
    };

    return { session: adminSession, error: null };
  } catch (error) {
    console.error('Admin login error:', error);
    return { session: null, error: error as Error };
  }
}

export async function getAdminSession(): Promise<AdminSession | null> {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error || !session) {
      return null;
    }

    return {
      user: {
        id: session.user.id,
        email: session.user.email || '',
        user_metadata: session.user.user_metadata,
      },
      access_token: session.access_token,
      refresh_token: session.refresh_token,
    };
  } catch (error) {
    console.error('Get admin session error:', error);
    return null;
  }
}

export async function adminLogout(): Promise<{ error: Error | null }> {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      return { error: new Error(error.message) };
    }
    return { error: null };
  } catch (error) {
    console.error('Admin logout error:', error);
    return { error: error as Error };
  }
}

export async function adminSignUp(email: string, password: string): Promise<{ user: any | null; error: Error | null }> {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });

    if (error) {
      return { user: null, error: new Error(error.message) };
    }

    return { user: data.user, error: null };
  } catch (error) {
    console.error('Admin signup error:', error);
    return { user: null, error: error as Error };
  }
}
