import React, { useState, useEffect } from 'react';
import { Users, CreditCard, DollarSign, Activity, Plus, Search } from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface DashboardStats {
  totalUsers: number;
  totalAccounts: number;
  totalBalance: number;
  recentTransactions: number;
}

interface AdminDashboardProps {
  onNavigate: (page: string) => void;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ onNavigate }) => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalAccounts: 0,
    totalBalance: 0,
    recentTransactions: 0,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      // Don't set loading to true - let UI show immediately
      
      // Get total users
      const { count: usersCount } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      // Get total accounts
      const { count: accountsCount } = await supabase
        .from('accounts')
        .select('*', { count: 'exact', head: true });

      // Get total balance
      const { data: balanceData } = await supabase
        .from('accounts')
        .select('balance');

      const totalBalance = balanceData?.reduce((sum, account) => sum + parseFloat(account.balance || '0'), 0) || 0;

      // Get recent transactions (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const { count: transactionsCount } = await supabase
        .from('transactions')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', sevenDaysAgo.toISOString());

      setStats({
        totalUsers: usersCount || 0,
        totalAccounts: accountsCount || 0,
        totalBalance,
        recentTransactions: transactionsCount || 0,
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    }
    // No finally block needed - no loading state to reset
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color }: {
    icon: any;
    title: string;
    value: string | number;
    subtitle: string;
    color: string;
  }) => (
    <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>{value}</p>
          <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
        </div>
        <div className={`p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          <Icon className={`h-6 w-6 ${color}`} />
        </div>
      </div>
    </div>
  );

  // Remove the loading spinner - show UI immediately

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage users, accounts, and system overview</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => onNavigate('users')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Add User</span>
          </button>
          <button
            onClick={() => onNavigate('accounts')}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <CreditCard className="h-4 w-4" />
            <span>Manage Accounts</span>
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={Users}
          title="Total Users"
          value={stats.totalUsers}
          subtitle="Registered users"
          color="text-blue-600"
        />
        <StatCard
          icon={CreditCard}
          title="Total Accounts"
          value={stats.totalAccounts}
          subtitle="Active accounts"
          color="text-green-600"
        />
        <StatCard
          icon={DollarSign}
          title="Total Balance"
          value={`$${stats.totalBalance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
          subtitle="Across all accounts"
          color="text-purple-600"
        />
        <StatCard
          icon={Activity}
          title="Recent Transactions"
          value={stats.recentTransactions}
          subtitle="Last 7 days"
          color="text-orange-600"
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => onNavigate('users')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors text-center"
          >
            <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">Manage Users</p>
            <p className="text-xs text-gray-500">Create, edit, or delete user accounts</p>
          </button>
          
          <button
            onClick={() => onNavigate('accounts')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors text-center"
          >
            <CreditCard className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">Account Management</p>
            <p className="text-xs text-gray-500">Add/remove money, manage balances</p>
          </button>
          
          <button
            onClick={() => onNavigate('transactions')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors text-center"
          >
            <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">View Transactions</p>
            <p className="text-xs text-gray-500">Monitor all system transactions</p>
          </button>
        </div>
      </div>
    </div>
  );
};
